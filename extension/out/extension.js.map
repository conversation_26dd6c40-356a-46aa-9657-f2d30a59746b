{"version": 3, "file": "extension.js", "sourceRoot": "", "sources": ["../src/extension.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;AAoBH,4BAoEC;AAED,gCAYC;AApGD,iCAAiC;AACjC,oDAAiD;AACjD,uEAAsE;AACtE,mEAAmE;AACnE,+CAAmD;AACnD,oDAAoD;AACpD,wDAAwD;AACxD,kDAAuD;AACvD,2CAAwC;AAExC,IAAI,WAAwB,CAAC;AAC7B,IAAI,iBAAuC,CAAC;AAC5C,IAAI,gBAAqC,CAAC;AAC1C,IAAI,eAAgC,CAAC;AACrC,IAAI,YAA6B,CAAC;AAClC,IAAI,cAAiC,CAAC;AACtC,IAAI,aAAmC,CAAC;AAExC,SAAgB,QAAQ,CAAC,OAAgC;IACrD,eAAM,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;IAElE,6BAA6B;IAC7B,WAAW,GAAG,yBAAW,CAAC,WAAW,EAAE,CAAC;IACxC,iBAAiB,GAAG,IAAI,yCAAoB,EAAE,CAAC;IAC/C,gBAAgB,GAAG,IAAI,sCAAmB,CAAC,OAAO,CAAC,CAAC;IACpD,eAAe,GAAG,IAAI,0BAAe,EAAE,CAAC;IACxC,YAAY,GAAG,IAAI,8BAAe,CAAC,OAAO,EAAE,WAAW,EAAE,eAAe,CAAC,CAAC;IAC1E,cAAc,GAAG,IAAI,kCAAiB,CAAC,WAAW,CAAC,CAAC;IAEpD,yBAAyB;IACzB,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,kBAAkB,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IACxF,aAAa,CAAC,OAAO,GAAG,eAAe,CAAC;IACxC,aAAa,CAAC,IAAI,GAAG,2BAA2B,CAAC;IACjD,aAAa,CAAC,OAAO,GAAG,gDAAgD,CAAC;IACzE,aAAa,CAAC,IAAI,EAAE,CAAC;IAErB,6BAA6B;IAC7B,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,MAAM,CAAC,MAAM,CAAC,2BAA2B,CAAC,SAAS,EAAE,YAAY,CAAC,CACrE,CAAC;IAEF,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,MAAM,CAAC,MAAM,CAAC,2BAA2B,CAAC,aAAa,EAAE,gBAAgB,CAAC,CAC7E,CAAC;IAEF,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,MAAM,CAAC,MAAM,CAAC,wBAAwB,CAAC,WAAW,EAAE,cAAc,CAAC,CACtE,CAAC;IAEF,+BAA+B;IAC/B,iBAAiB,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;IAEpC,oBAAoB;IACpB,IAAA,2BAAgB,EAAC,OAAO,EAAE,WAAW,EAAE,YAAY,EAAE,gBAAgB,EAAE,eAAe,CAAC,CAAC;IAExF,oCAAoC;IACpC,UAAU,CAAC,KAAK,IAAI,EAAE;QAClB,IAAI,CAAC;YACD,aAAa,CAAC,IAAI,GAAG,oCAAoC,CAAC;YAC1D,MAAM,MAAM,GAAG,MAAM,WAAW,CAAC,SAAS,EAAE,CAAC;YAE7C,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBACjB,eAAe,CAAC,IAAI,CAAC,CAAC;gBACtB,eAAM,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;gBACnD,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,qDAAqD,CAAC,CAAC;YAChG,CAAC;iBAAM,CAAC;gBACJ,eAAe,CAAC,KAAK,CAAC,CAAC;gBACvB,eAAM,CAAC,KAAK,CAAC,sCAAsC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;gBACnE,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,oEAAoE,CAAC,CAAC;YAC3G,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAe,CAAC,KAAK,CAAC,CAAC;YACvB,eAAM,CAAC,KAAK,CAAC,uBAAuB,KAAK,EAAE,CAAC,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,oEAAoE,CAAC,CAAC;QAC3G,CAAC;IACL,CAAC,EAAE,IAAI,CAAC,CAAC;IAIT,+BAA+B;IAC/B,0BAA0B,CAAC,OAAO,CAAC,CAAC;IAEpC,wCAAwC;IACxC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAE1C,eAAM,CAAC,IAAI,CAAC,yDAAyD,CAAC,CAAC;AAC3E,CAAC;AAED,SAAgB,UAAU;IACtB,eAAM,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;IAEpE,IAAI,iBAAiB,EAAE,CAAC;QACpB,iBAAiB,CAAC,UAAU,EAAE,CAAC;IACnC,CAAC;IAED,IAAI,aAAa,EAAE,CAAC;QAChB,aAAa,CAAC,OAAO,EAAE,CAAC;IAC5B,CAAC;IAED,eAAM,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;AAChE,CAAC;AAED,SAAS,eAAe,CAAC,SAAkB;IACvC,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;IACxD,MAAM,SAAS,GAAG,MAAM,CAAC,GAAG,CAAS,WAAW,EAAE,KAAK,CAAC,CAAC;IACzD,MAAM,oBAAoB,GAAG,MAAM,CAAC,GAAG,CAAU,sBAAsB,EAAE,IAAI,CAAC,CAAC;IAE/E,IAAI,SAAS,GAAG,UAAU,CAAC;IAC3B,IAAI,SAAS,GAAG,KAAK,CAAC;IAEtB,QAAQ,SAAS,EAAE,CAAC;QAChB,KAAK,UAAU;YACX,SAAS,GAAG,SAAS,CAAC;YACtB,SAAS,GAAG,UAAU,CAAC;YACvB,MAAM;QACV,KAAK,UAAU;YACX,SAAS,GAAG,kBAAkB,CAAC;YAC/B,SAAS,GAAG,QAAQ,CAAC;YACrB,MAAM;QACV;YACI,SAAS,GAAG,UAAU,CAAC;YACvB,SAAS,GAAG,KAAK,CAAC;IAC1B,CAAC;IAED,IAAI,SAAS,EAAE,CAAC;QACZ,aAAa,CAAC,IAAI,GAAG,oBAAoB,CAAC,CAAC;YACvC,GAAG,SAAS,IAAI,SAAS,aAAa,CAAC,CAAC;YACxC,GAAG,SAAS,YAAY,CAAC;QAC7B,aAAa,CAAC,OAAO,GAAG,GAAG,SAAS,8EAA8E,CAAC;QACnH,aAAa,CAAC,OAAO,GAAG,cAAc,CAAC;QACvC,aAAa,CAAC,eAAe,GAAG,SAAS,CAAC;IAC9C,CAAC;SAAM,CAAC;QACJ,aAAa,CAAC,IAAI,GAAG,oBAAoB,CAAC,CAAC;YACvC,GAAG,SAAS,IAAI,SAAS,gBAAgB,CAAC,CAAC;YAC3C,GAAG,SAAS,eAAe,CAAC;QAChC,aAAa,CAAC,OAAO,GAAG,GAAG,SAAS,+FAA+F,CAAC;QACpI,aAAa,CAAC,OAAO,GAAG,eAAe,CAAC;QACxC,aAAa,CAAC,eAAe,GAAG,IAAI,MAAM,CAAC,UAAU,CAAC,iCAAiC,CAAC,CAAC;IAC7F,CAAC;AACL,CAAC;AAED,wDAAwD;AACxD,SAAS,0BAA0B,CAAC,OAAgC;IAChE,MAAM,cAAc,GAAG,MAAM,CAAC,SAAS,CAAC,wBAAwB,CAAC,KAAK,CAAC,EAAE;QACrE,IAAI,KAAK,CAAC,oBAAoB,CAAC,eAAe,CAAC;YAC3C,KAAK,CAAC,oBAAoB,CAAC,0BAA0B,CAAC,EAAE,CAAC;YACzD,eAAe,CAAC,WAAW,CAAC,aAAa,EAAE,CAAC,CAAC;QACjD,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;AAC/C,CAAC"}