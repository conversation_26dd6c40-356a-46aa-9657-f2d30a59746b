"use strict";
/**
 * HRM Model Loader for VSCode Extension
 * Loads and manages the safetensors HRM model locally
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ModelLoader = exports.HRMModel = void 0;
const vscode = require("vscode");
const logger_1 = require("../utils/logger");
class HRMModel {
    constructor(config, modelPath) {
        this.loaded = false;
        this.config = config;
        this.modelPath = modelPath;
    }
    getConfig() {
        return this.config;
    }
    isLoaded() {
        return this.loaded;
    }
    getModelPath() {
        return this.modelPath;
    }
    async generateText(prompt, options) {
        if (!this.loaded) {
            throw new Error('Model not loaded');
        }
        // For now, we'll use the Python model server
        // In the future, this could be replaced with direct model inference
        const response = await this.callPythonServer(prompt, options);
        return response;
    }
    async callPythonServer(prompt, options) {
        var _a;
        try {
            // Use the configured server URL
            const config = vscode.workspace.getConfiguration('hrm');
            const serverUrl = config.get('serverUrl', 'http://localhost:5001');
            const fetch = (await Promise.resolve().then(() => require('node-fetch'))).default;
            const response = await fetch(`${serverUrl}/chat`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    messages: [{ role: 'user', content: prompt }],
                    max_tokens: (options === null || options === void 0 ? void 0 : options.maxTokens) || 1000,
                    temperature: (options === null || options === void 0 ? void 0 : options.temperature) || 0.7,
                    stream: (options === null || options === void 0 ? void 0 : options.stream) || false,
                    model_mode: this.getModelMode(),
                    model_path: this.getActiveModelPath()
                })
            });
            const data = await response.json();
            return ((_a = data.message) === null || _a === void 0 ? void 0 : _a.content) || 'No response generated';
        }
        catch (error) {
            logger_1.Logger.error(`Model server call failed: ${error}`);
            return `Error: ${error}`;
        }
    }
    setLoaded(loaded) {
        this.loaded = loaded;
    }
    getModelMode() {
        const config = vscode.workspace.getConfiguration('hrm');
        return config.get('modelMode', 'hrm');
    }
    getActiveModelPath() {
        const config = vscode.workspace.getConfiguration('hrm');
        const mode = this.getModelMode();
        switch (mode) {
            case 'deepseek':
                return config.get('deepseekModelPath', 'models/DeepSeek-Coder-V2-Lite-Base');
            case 'hf-local':
                return config.get('hfLocalModelPath', '');
            default:
                return undefined; // HRM model uses default server path
        }
    }
}
exports.HRMModel = HRMModel;
class ModelLoader {
    constructor() {
        this.currentModel = null;
        // Constructor now only initializes the instance
    }
    getServerUrl() {
        const config = vscode.workspace.getConfiguration('hrm');
        return config.get('serverUrl', 'http://localhost:5002');
    }
    static getInstance() {
        if (!ModelLoader.instance) {
            ModelLoader.instance = new ModelLoader();
        }
        return ModelLoader.instance;
    }
    async loadModel() {
        try {
            logger_1.Logger.info('🔄 Connecting to HRM server...');
            // First, check if server is running
            const serverUrl = this.getServerUrl();
            try {
                const fetch = (await Promise.resolve().then(() => require('node-fetch'))).default;
                const response = await fetch(`${serverUrl}/health`);
                if (!response.ok) {
                    throw new Error('Server not responding');
                }
                logger_1.Logger.info('✅ HRM server is running');
            }
            catch (error) {
                logger_1.Logger.info('❌ HRM server not running, please start it manually');
                logger_1.Logger.info('💡 Run: python3 start_hrm_server.py');
                return { success: false, error: 'HRM server not running. Please start it with: python3 start_hrm_server.py' };
            }
            // Try to get model status from server
            try {
                const fetch = (await Promise.resolve().then(() => require('node-fetch'))).default;
                const response = await fetch(`${serverUrl}/status`);
                const status = await response.json();
                if (!status.model_loaded) {
                    const error = 'No model loaded on server';
                    logger_1.Logger.error(error);
                    return { success: false, error };
                }
                // Create model config based on server status
                const modelConfig = {
                    model_name: 'hrm_model',
                    version: 'v1.0.0',
                    architecture: 'HRM',
                    config: {
                        vocab_size: 32000,
                        hidden_size: 2048,
                        num_layers: 24,
                        num_attention_heads: 32,
                        max_sequence_length: 4096,
                        multimodal_enabled: true,
                        streaming_enabled: true
                    }
                };
                // Create model instance
                this.currentModel = new HRMModel(modelConfig, 'server-based');
                this.currentModel.setLoaded(true);
                logger_1.Logger.info(`✅ Connected to HRM server successfully!`);
                logger_1.Logger.info(`   • Model: ${modelConfig.model_name} ${modelConfig.version}`);
                logger_1.Logger.info(`   • Server: ${serverUrl}`);
                logger_1.Logger.info(`   • Status: Ready for inference`);
                // Set context for when clauses
                vscode.commands.executeCommand('setContext', 'hrm.modelLoaded', true);
                return { success: true, model: this.currentModel };
            }
            catch (error) {
                const errorMsg = `Failed to connect to server: ${error}`;
                logger_1.Logger.error(errorMsg);
                return { success: false, error: errorMsg };
            }
        }
        catch (error) {
            const errorMsg = `Failed to load model: ${error}`;
            logger_1.Logger.error(errorMsg);
            return { success: false, error: errorMsg };
        }
    }
    getCurrentModel() {
        return this.currentModel;
    }
    isModelLoaded() {
        return this.currentModel !== null && this.currentModel.isLoaded();
    }
    async reloadModel() {
        this.currentModel = null;
        vscode.commands.executeCommand('setContext', 'hrm.modelLoaded', false);
        return await this.loadModel();
    }
    updateModelPath(newPath) {
        // Update configuration only (no longer storing locally)
        const config = vscode.workspace.getConfiguration('hrm');
        config.update('modelPath', newPath, vscode.ConfigurationTarget.Global);
    }
    getParameterCount(config) {
        // Rough estimation based on architecture
        const hiddenSize = config.config.hidden_size;
        const numLayers = config.config.num_layers;
        const vocabSize = config.config.vocab_size;
        // Simplified parameter count estimation
        const embeddingParams = vocabSize * hiddenSize;
        const layerParams = numLayers * (hiddenSize * hiddenSize * 4); // Rough estimate
        const totalParams = embeddingParams + layerParams;
        return Math.round(totalParams / 1000000); // Convert to millions
    }
    getFeatureList(config) {
        const features = [];
        if (config.config.multimodal_enabled)
            features.push('Multi-modal');
        if (config.config.streaming_enabled)
            features.push('Streaming');
        features.push(`${config.config.num_layers} layers`);
        features.push(`${config.config.max_sequence_length} context`);
        return features.join(', ');
    }
    getModelStats() {
        if (!this.currentModel) {
            return null;
        }
        const config = this.currentModel.getConfig();
        return {
            name: config.model_name,
            version: config.version,
            architecture: config.architecture,
            parameters: this.getParameterCount(config),
            features: this.getFeatureList(config),
            path: this.currentModel.getModelPath(),
            loaded: this.currentModel.isLoaded()
        };
    }
}
exports.ModelLoader = ModelLoader;
//# sourceMappingURL=modelLoader.js.map