{"version": 3, "file": "modelLoader.js", "sourceRoot": "", "sources": ["../../src/core/modelLoader.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,iCAAiC;AACjC,4CAAyC;AAuBzC,MAAa,QAAQ;IAKjB,YAAY,MAAsB,EAAE,SAAiB;QAF7C,WAAM,GAAY,KAAK,CAAC;QAG5B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC/B,CAAC;IAEM,SAAS;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAEM,QAAQ;QACX,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAEM,YAAY;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAEM,KAAK,CAAC,YAAY,CAAC,MAAc,EAAE,OAIzC;QACG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;QACxC,CAAC;QAED,6CAA6C;QAC7C,oEAAoE;QACpE,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAC9D,OAAO,QAAQ,CAAC;IACpB,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,MAAc,EAAE,OAAa;;QACxD,IAAI,CAAC;YACD,gCAAgC;YAChC,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;YACxD,MAAM,SAAS,GAAG,MAAM,CAAC,GAAG,CAAC,WAAW,EAAE,uBAAuB,CAAC,CAAC;YAEnE,MAAM,KAAK,GAAG,CAAC,2CAAa,YAAY,EAAC,CAAC,CAAC,OAAO,CAAC;YACnD,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,SAAS,OAAO,EAAE;gBAC9C,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE;oBACL,cAAc,EAAE,kBAAkB;iBACrC;gBACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;oBACjB,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;oBAC7C,UAAU,EAAE,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,SAAS,KAAI,IAAI;oBACtC,WAAW,EAAE,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,WAAW,KAAI,GAAG;oBACxC,MAAM,EAAE,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM,KAAI,KAAK;oBAChC,UAAU,EAAE,IAAI,CAAC,YAAY,EAAE;oBAC/B,UAAU,EAAE,IAAI,CAAC,kBAAkB,EAAE;iBACxC,CAAC;aACL,CAAC,CAAC;YAEH,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAS,CAAC;YAC1C,OAAO,CAAA,MAAA,IAAI,CAAC,OAAO,0CAAE,OAAO,KAAI,uBAAuB,CAAC;QAC5D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,6BAA6B,KAAK,EAAE,CAAC,CAAC;YACnD,OAAO,UAAU,KAAK,EAAE,CAAC;QAC7B,CAAC;IACL,CAAC;IAMM,SAAS,CAAC,MAAe;QAC5B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACzB,CAAC;IAEO,YAAY;QAChB,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QACxD,OAAO,MAAM,CAAC,GAAG,CAAS,WAAW,EAAE,KAAK,CAAC,CAAC;IAClD,CAAC;IAEO,kBAAkB;QACtB,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QACxD,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAEjC,QAAQ,IAAI,EAAE,CAAC;YACX,KAAK,UAAU;gBACX,OAAO,MAAM,CAAC,GAAG,CAAS,mBAAmB,EAAE,oCAAoC,CAAC,CAAC;YACzF,KAAK,UAAU;gBACX,OAAO,MAAM,CAAC,GAAG,CAAS,kBAAkB,EAAE,EAAE,CAAC,CAAC;YACtD;gBACI,OAAO,SAAS,CAAC,CAAC,qCAAqC;QAC/D,CAAC;IACL,CAAC;CACJ;AA7FD,4BA6FC;AAED,MAAa,WAAW;IAIpB;QAFQ,iBAAY,GAAoB,IAAI,CAAC;QAGzC,gDAAgD;IACpD,CAAC;IAEO,YAAY;QAChB,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QACxD,OAAO,MAAM,CAAC,GAAG,CAAC,WAAW,EAAE,uBAAuB,CAAC,CAAC;IAC5D,CAAC;IAEM,MAAM,CAAC,WAAW;QACrB,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;YACxB,WAAW,CAAC,QAAQ,GAAG,IAAI,WAAW,EAAE,CAAC;QAC7C,CAAC;QACD,OAAO,WAAW,CAAC,QAAQ,CAAC;IAChC,CAAC;IAEM,KAAK,CAAC,SAAS;QAClB,IAAI,CAAC;YACD,eAAM,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;YAE9C,oCAAoC;YACpC,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;YACtC,IAAI,CAAC;gBACD,MAAM,KAAK,GAAG,CAAC,2CAAa,YAAY,EAAC,CAAC,CAAC,OAAO,CAAC;gBACnD,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,SAAS,SAAS,CAAC,CAAC;gBACpD,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;oBACf,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;gBAC7C,CAAC;gBACD,eAAM,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;YAC3C,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,eAAM,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;gBAClE,eAAM,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;gBACnD,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,2EAA2E,EAAE,CAAC;YAClH,CAAC;YAED,sCAAsC;YACtC,IAAI,CAAC;gBACD,MAAM,KAAK,GAAG,CAAC,2CAAa,YAAY,EAAC,CAAC,CAAC,OAAO,CAAC;gBACnD,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,SAAS,SAAS,CAAC,CAAC;gBACpD,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAS,CAAC;gBAE5C,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;oBACvB,MAAM,KAAK,GAAG,2BAA2B,CAAC;oBAC1C,eAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;oBACpB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;gBACrC,CAAC;gBAED,6CAA6C;gBAC7C,MAAM,WAAW,GAAmB;oBAChC,UAAU,EAAE,WAAW;oBACvB,OAAO,EAAE,QAAQ;oBACjB,YAAY,EAAE,KAAK;oBACnB,MAAM,EAAE;wBACJ,UAAU,EAAE,KAAK;wBACjB,WAAW,EAAE,IAAI;wBACjB,UAAU,EAAE,EAAE;wBACd,mBAAmB,EAAE,EAAE;wBACvB,mBAAmB,EAAE,IAAI;wBACzB,kBAAkB,EAAE,IAAI;wBACxB,iBAAiB,EAAE,IAAI;qBAC1B;iBACJ,CAAC;gBAEF,wBAAwB;gBACxB,IAAI,CAAC,YAAY,GAAG,IAAI,QAAQ,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;gBAC9D,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBAElC,eAAM,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;gBACvD,eAAM,CAAC,IAAI,CAAC,eAAe,WAAW,CAAC,UAAU,IAAI,WAAW,CAAC,OAAO,EAAE,CAAC,CAAC;gBAC5E,eAAM,CAAC,IAAI,CAAC,gBAAgB,SAAS,EAAE,CAAC,CAAC;gBACzC,eAAM,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;gBAEhD,+BAA+B;gBAC/B,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,YAAY,EAAE,iBAAiB,EAAE,IAAI,CAAC,CAAC;gBAEtE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC;YAEvD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,MAAM,QAAQ,GAAG,gCAAgC,KAAK,EAAE,CAAC;gBACzD,eAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;gBACvB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC;YAC/C,CAAC;QAEL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,QAAQ,GAAG,yBAAyB,KAAK,EAAE,CAAC;YAClD,eAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YACvB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC;QAC/C,CAAC;IACL,CAAC;IAEM,eAAe;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAEM,aAAa;QAChB,OAAO,IAAI,CAAC,YAAY,KAAK,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;IACtE,CAAC;IAEM,KAAK,CAAC,WAAW;QACpB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,YAAY,EAAE,iBAAiB,EAAE,KAAK,CAAC,CAAC;QACvE,OAAO,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC;IAClC,CAAC;IAEM,eAAe,CAAC,OAAe;QAClC,wDAAwD;QACxD,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QACxD,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,OAAO,EAAE,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;IAC3E,CAAC;IAEO,iBAAiB,CAAC,MAAsB;QAC5C,yCAAyC;QACzC,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC;QAC7C,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC;QAC3C,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC;QAE3C,wCAAwC;QACxC,MAAM,eAAe,GAAG,SAAS,GAAG,UAAU,CAAC;QAC/C,MAAM,WAAW,GAAG,SAAS,GAAG,CAAC,UAAU,GAAG,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,iBAAiB;QAChF,MAAM,WAAW,GAAG,eAAe,GAAG,WAAW,CAAC;QAElD,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,OAAO,CAAC,CAAC,CAAC,sBAAsB;IACpE,CAAC;IAEO,cAAc,CAAC,MAAsB;QACzC,MAAM,QAAQ,GAAG,EAAE,CAAC;QACpB,IAAI,MAAM,CAAC,MAAM,CAAC,kBAAkB;YAAE,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACnE,IAAI,MAAM,CAAC,MAAM,CAAC,iBAAiB;YAAE,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAChE,QAAQ,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,UAAU,SAAS,CAAC,CAAC;QACpD,QAAQ,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAmB,UAAU,CAAC,CAAC;QAC9D,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAEM,aAAa;QAChB,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACrB,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC;QAC7C,OAAO;YACH,IAAI,EAAE,MAAM,CAAC,UAAU;YACvB,OAAO,EAAE,MAAM,CAAC,OAAO;YACvB,YAAY,EAAE,MAAM,CAAC,YAAY;YACjC,UAAU,EAAE,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC;YAC1C,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC;YACrC,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE;YACtC,MAAM,EAAE,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE;SACvC,CAAC;IACN,CAAC;CACJ;AAzJD,kCAyJC"}