"use strict";
/**
 * HRM Coding Assistant VSCode Extension
 * Main entry point for the extension with agentic capabilities
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.activate = activate;
exports.deactivate = deactivate;
const vscode = require("vscode");
const modelLoader_1 = require("./core/modelLoader");
const completionProvider_1 = require("./providers/completionProvider");
const planningProvider_1 = require("./providers/planningProvider");
const mcpTools_1 = require("./tools/mcpTools");
const chatProvider_1 = require("./ui/chatProvider");
const statusProvider_1 = require("./ui/statusProvider");
const commands_1 = require("./commands/commands");
const logger_1 = require("./utils/logger");
let modelLoader;
let completionManager;
let planningProvider;
let mcpToolsManager;
let chatProvider;
let statusProvider;
let statusBarItem;
function activate(context) {
    logger_1.Logger.info('🚀 HRM Coding Assistant extension is activating...');
    // Initialize core components
    modelLoader = modelLoader_1.ModelLoader.getInstance();
    completionManager = new completionProvider_1.HRMCompletionManager();
    planningProvider = new planningProvider_1.HRMPlanningProvider(context);
    mcpToolsManager = new mcpTools_1.MCPToolsManager();
    chatProvider = new chatProvider_1.HRMChatProvider(context, modelLoader, mcpToolsManager);
    statusProvider = new statusProvider_1.HRMStatusProvider(modelLoader);
    // Create status bar item
    statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100);
    statusBarItem.command = 'hrm.loadModel';
    statusBarItem.text = '$(robot) HRM: Starting...';
    statusBarItem.tooltip = 'HRM Coding Assistant - Connecting to server...';
    statusBarItem.show();
    // Register webview providers
    context.subscriptions.push(vscode.window.registerWebviewViewProvider('hrmChat', chatProvider));
    context.subscriptions.push(vscode.window.registerWebviewViewProvider('hrmPlanning', planningProvider));
    context.subscriptions.push(vscode.window.registerTreeDataProvider('hrmStatus', statusProvider));
    // Activate completion provider
    completionManager.activate(context);
    // Register commands
    (0, commands_1.registerCommands)(context, modelLoader, chatProvider, planningProvider, mcpToolsManager);
    // Auto-connect to server on startup
    setTimeout(async () => {
        try {
            statusBarItem.text = '$(loading~spin) HRM: Connecting...';
            const result = await modelLoader.loadModel();
            if (result.success) {
                updateStatusBar(true);
                logger_1.Logger.info('✅ HRM server connected successfully');
                vscode.window.showInformationMessage('🤖 HRM Coding Assistant connected! Server is ready.');
            }
            else {
                updateStatusBar(false);
                logger_1.Logger.error(`❌ Failed to connect to HRM server: ${result.error}`);
                vscode.window.showWarningMessage(`HRM Server not running. Start it with: python3 start_hrm_server.py`);
            }
        }
        catch (error) {
            updateStatusBar(false);
            logger_1.Logger.error(`❌ Connection error: ${error}`);
            vscode.window.showWarningMessage(`HRM Server not running. Start it with: python3 start_hrm_server.py`);
        }
    }, 2000);
    // Setup configuration listener
    setupConfigurationListener(context);
    // Register status bar item for disposal
    context.subscriptions.push(statusBarItem);
    logger_1.Logger.info('✅ HRM Coding Assistant extension activated successfully');
}
function deactivate() {
    logger_1.Logger.info('🔄 HRM Coding Assistant extension is deactivating...');
    if (completionManager) {
        completionManager.deactivate();
    }
    if (statusBarItem) {
        statusBarItem.dispose();
    }
    logger_1.Logger.info('✅ HRM Coding Assistant extension deactivated');
}
function updateStatusBar(connected) {
    const config = vscode.workspace.getConfiguration('hrm');
    const modelMode = config.get('modelMode', 'hrm');
    const showModelInStatusBar = config.get('showModelInStatusBar', true);
    let modelIcon = '$(robot)';
    let modelName = 'HRM';
    switch (modelMode) {
        case 'deepseek':
            modelIcon = '$(code)';
            modelName = 'DeepSeek';
            break;
        case 'hf-local':
            modelIcon = '$(settings-gear)';
            modelName = 'Custom';
            break;
        default:
            modelIcon = '$(robot)';
            modelName = 'HRM';
    }
    if (connected) {
        statusBarItem.text = showModelInStatusBar ?
            `${modelIcon} ${modelName}: Connected` :
            `${modelIcon} Connected`;
        statusBarItem.tooltip = `${modelName} model connected and ready\nClick to open chat\nRight-click to switch models`;
        statusBarItem.command = 'hrm.openChat';
        statusBarItem.backgroundColor = undefined;
    }
    else {
        statusBarItem.text = showModelInStatusBar ?
            `${modelIcon} ${modelName}: Disconnected` :
            `${modelIcon} Disconnected`;
        statusBarItem.tooltip = `${modelName} model server not running\nStart with: python3 start_hrm_server.py\nClick to retry connection`;
        statusBarItem.command = 'hrm.loadModel';
        statusBarItem.backgroundColor = new vscode.ThemeColor('statusBarItem.warningBackground');
    }
}
// Listen for configuration changes to update status bar
function setupConfigurationListener(context) {
    const configListener = vscode.workspace.onDidChangeConfiguration(event => {
        if (event.affectsConfiguration('hrm.modelMode') ||
            event.affectsConfiguration('hrm.showModelInStatusBar')) {
            updateStatusBar(modelLoader.isModelLoaded());
        }
    });
    context.subscriptions.push(configListener);
}
//# sourceMappingURL=extension.js.map