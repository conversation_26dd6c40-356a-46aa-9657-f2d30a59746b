{"name": "hrm-coding-assistant", "displayName": "HRM Coding Assistant", "description": "AI-powered coding assistant with agentic capabilities using Hierarchical Reasoning Model", "version": "2.0.0", "publisher": "hrm-ai", "engines": {"vscode": "^1.74.0"}, "categories": ["Other", "Machine Learning", "Snippets", "Programming Languages"], "keywords": ["ai", "coding", "assistant", "hrm", "code-generation", "agentic", "completion", "planning", "mcp-tools"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "hrm.generateCode", "title": "Generate Code", "category": "HRM"}, {"command": "hrm.openChat", "title": "Open Chat", "category": "HRM"}, {"command": "hrm.loadModel", "title": "Load HRM Model", "category": "HRM"}, {"command": "hrm.planTask", "title": "Plan Task with HRM", "category": "HRM"}, {"command": "hrm.executeTask", "title": "Execute Planned Task", "category": "HRM"}, {"command": "hrm.explainCode", "title": "Explain Code", "category": "HRM"}, {"command": "hrm.refactorCode", "title": "Refactor Code", "category": "HRM"}, {"command": "hrm.generateTests", "title": "Generate Tests", "category": "HRM"}, {"command": "hrm.analyzeWorkspace", "title": "Analyze Workspace", "category": "HRM"}, {"command": "hrm.selectModel", "title": "Select Model (HRM or DeepSeek)", "category": "HRM"}, {"command": "hrm.toggleModel", "title": "Toggle Model (Quick Switch)", "category": "HRM"}, {"command": "hrm.modelStatus", "title": "Show Model Status", "category": "HRM"}], "viewsContainers": {"activitybar": [{"id": "hrm", "title": "HRM Assistant", "icon": "$(robot)"}]}, "views": {"hrm": [{"id": "hrmChat", "name": "Cha<PERSON>", "type": "webview"}, {"id": "hrmPlanning", "name": "Task Planning", "type": "webview", "when": "hrm.modelLoaded"}, {"id": "hrmStatus", "name": "Status"}]}, "menus": {"editor/context": [{"command": "hrm.explainCode", "when": "editorHasSelection", "group": "hrm@1"}, {"command": "hrm.refactorCode", "when": "editorHasSelection", "group": "hrm@2"}, {"command": "hrm.generateTests", "when": "editorHasSelection", "group": "hrm@3"}], "explorer/context": [{"command": "hrm.analyzeWorkspace", "when": "explorerResourceIsFolder", "group": "hrm@1"}]}, "keybindings": [{"command": "hrm.openChat", "key": "ctrl+shift+h", "mac": "cmd+shift+h"}, {"command": "hrm.generateCode", "key": "ctrl+shift+g", "mac": "cmd+shift+g"}, {"command": "hrm.planTask", "key": "ctrl+shift+p", "mac": "cmd+shift+p"}, {"command": "hrm.toggleModel", "key": "ctrl+shift+m", "mac": "cmd+shift+m"}], "configuration": {"title": "HRM Coding Assistant", "properties": {"hrm.serverUrl": {"type": "string", "default": "http://localhost:5002", "description": "HRM server URL - make sure the server is running with: python3 start_hrm_server.py"}, "hrm.modelPath": {"type": "string", "default": "../models/hrm_model_v1.0.0", "description": "Path to the HRM model directory"}, "hrm.enableCodeCompletion": {"type": "boolean", "default": true, "description": "Enable AI-powered code completion"}, "hrm.enableAgentic": {"type": "boolean", "default": true, "description": "Enable agentic capabilities (planning, execution)"}, "hrm.enableMCPTools": {"type": "boolean", "default": true, "description": "Enable MCP tools integration"}, "hrm.maxTokens": {"type": "number", "default": 1000, "description": "Maximum tokens for generation"}, "hrm.temperature": {"type": "number", "default": 0.7, "minimum": 0, "maximum": 2, "description": "Temperature for text generation"}, "hrm.modelMode": {"type": "string", "enum": ["hrm", "deepseek", "hf-local"], "default": "hrm", "description": "Active model: 'hrm' (custom HRM model), 'deepseek' (DeepSeek Coder), or 'hf-local' (custom HF model)"}, "hrm.deepseekModelPath": {"type": "string", "default": "models/DeepSeek-Coder-V2-Lite-Base", "description": "Path to the DeepSeek Coder model directory"}, "hrm.hfLocalModelPath": {"type": "string", "default": "", "description": "Path to a custom local HuggingFace Causal LM when modelMode = 'hf-local'"}, "hrm.chatTheme": {"type": "string", "enum": ["dark", "light", "auto"], "default": "auto", "description": "Chat interface theme"}, "hrm.enableStreaming": {"type": "boolean", "default": true, "description": "Enable streaming responses in chat"}, "hrm.showModelInStatusBar": {"type": "boolean", "default": true, "description": "Show current model in status bar"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "package": "vsce package", "test": "node ./out/test/runTest.js"}, "devDependencies": {"@types/node": "^20.x", "@types/vscode": "^1.74.0", "@vscode/test-electron": "^2.3.8", "@vscode/vsce": "^3.6.0", "typescript": "^5.3.0", "vsce": "^2.15.0"}, "dependencies": {"@tensorflow/tfjs-node": "^4.15.0", "axios": "^1.6.0", "node-fetch": "^3.3.2"}}