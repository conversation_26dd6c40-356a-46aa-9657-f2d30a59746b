import * as vscode from 'vscode';
import { ModelLoader } from '../core/modelLoader';
import { MCPToolsManager } from '../tools/mcpTools';
import { Logger } from '../utils/logger';

interface ChatMessage {
    id: string;
    role: 'user' | 'assistant' | 'system';
    content: string;
    timestamp: number;
    isStreaming?: boolean;
    error?: string;
    metadata?: {
        model?: string;
        tokens?: number;
        responseTime?: number;
    };
}

interface ChatSession {
    id: string;
    name: string;
    messages: ChatMessage[];
    created: number;
    lastUpdated: number;
}

export class HRMChatProvider implements vscode.WebviewViewProvider {
    private webviewView?: vscode.WebviewView;
    private currentSession: ChatSession;
    private sessions: Map<string, ChatSession> = new Map();
    private messageIdCounter = 0;

    constructor(
        private readonly context: vscode.ExtensionContext,
        private readonly modelLoader: ModelLoader,
        private readonly mcpToolsManager: MCPToolsManager
    ) {
        // Initialize default session
        this.currentSession = this.createNewSession('Default Chat');
        this.loadSessions();
    }

    public resolveWebviewView(webviewView: vscode.WebviewView) {
        this.webviewView = webviewView;

        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [this.context.extensionUri]
        };

        webviewView.webview.html = this.getHtmlForWebview();

        webviewView.webview.onDidReceiveMessage(async (data) => {
            switch (data.type) {
                case 'sendMessage':
                    await this.handleChatMessage(data.message, data.context);
                    break;
                case 'clearChat':
                    this.clearCurrentSession();
                    break;
                case 'newSession':
                    this.createAndSwitchToNewSession();
                    break;
                case 'switchSession':
                    this.switchToSession(data.sessionId);
                    break;
                case 'deleteSession':
                    this.deleteSession(data.sessionId);
                    break;
                case 'regenerateMessage':
                    await this.regenerateMessage(data.messageId);
                    break;
                case 'copyMessage':
                    await this.copyMessageToClipboard(data.content);
                    break;
                case 'exportChat':
                    await this.exportCurrentSession();
                    break;
                case 'ready':
                    this.sendInitialData();
                    break;
            }
        });
    }

    private async handleChatMessage(message: string, context?: any): Promise<void> {
        if (!this.modelLoader.isModelLoaded()) {
            this.sendErrorMessage('Please load a model first. Use Ctrl+Shift+M to select a model.');
            return;
        }

        const startTime = Date.now();
        const userMessage: ChatMessage = {
            id: this.generateMessageId(),
            role: 'user',
            content: message,
            timestamp: startTime
        };

        // Add user message to session and webview
        this.currentSession.messages.push(userMessage);
        this.sendMessageUpdate(userMessage);

        // Create assistant message placeholder for streaming
        const assistantMessage: ChatMessage = {
            id: this.generateMessageId(),
            role: 'assistant',
            content: '',
            timestamp: Date.now(),
            isStreaming: true
        };

        this.currentSession.messages.push(assistantMessage);
        this.sendMessageUpdate(assistantMessage);

        try {
            // Check if message contains tool requests
            const toolRequest = this.parseToolRequest(message);
            let response: string;
            let metadata: any = {};

            if (toolRequest) {
                // Execute MCP tool
                try {
                    const toolResult = await this.mcpToolsManager.executeTool(
                        toolRequest.toolName,
                        toolRequest.parameters
                    );
                    response = this.formatToolResult(toolResult);
                    metadata.tool = toolRequest.toolName;
                } catch (error) {
                    response = `❌ Tool execution failed: ${error}`;
                    assistantMessage.error = String(error);
                }
            } else {
                // Regular chat with model
                const model = this.modelLoader.getCurrentModel();
                if (!model) {
                    response = '❌ Model not available';
                    assistantMessage.error = 'Model not loaded';
                } else {
                    // Create context-aware prompt
                    const contextPrompt = this.createContextPrompt(message, context);

                    const config = vscode.workspace.getConfiguration('hrm');
                    const enableStreaming = config.get<boolean>('enableStreaming', true);

                    if (enableStreaming) {
                        response = await this.generateStreamingResponse(model, contextPrompt, assistantMessage);
                    } else {
                        response = await model.generateText(contextPrompt, {
                            maxTokens: 1000,
                            temperature: 0.7
                        });
                    }

                    metadata.model = config.get<string>('modelMode', 'hrm');
                    metadata.responseTime = Date.now() - startTime;
                }
            }

            // Update assistant message
            assistantMessage.content = response;
            assistantMessage.isStreaming = false;
            assistantMessage.metadata = metadata;

            this.sendMessageUpdate(assistantMessage);
            this.saveSessions();

        } catch (error) {
            Logger.error('Chat error: ' + error);
            assistantMessage.content = `❌ Error: ${error}`;
            assistantMessage.error = String(error);
            assistantMessage.isStreaming = false;
            this.sendMessageUpdate(assistantMessage);
        }
    }

    private async generateStreamingResponse(model: any, prompt: string, message: ChatMessage): Promise<string> {
        // For now, simulate streaming by generating full response and sending it in chunks
        // TODO: Implement true streaming when model supports it
        const response = await model.generateText(prompt, {
            maxTokens: 1000,
            temperature: 0.7
        });

        // Simulate streaming by sending chunks
        const words = response.split(' ');
        let currentContent = '';

        for (let i = 0; i < words.length; i++) {
            currentContent += (i > 0 ? ' ' : '') + words[i];
            message.content = currentContent;
            this.sendMessageUpdate(message);

            // Small delay to simulate streaming
            await new Promise(resolve => setTimeout(resolve, 50));
        }

        return response;
    }

    private parseToolRequest(message: string): { toolName: string; parameters: any } | null {
        const toolPatterns = [
            { pattern: /read file (.+)/i, tool: 'read_file', param: 'filePath' },
            { pattern: /list files in (.+)/i, tool: 'list_files', param: 'directoryPath' },
            { pattern: /search for (.+)/i, tool: 'search_workspace', param: 'query' },
            { pattern: /get workspace info/i, tool: 'get_workspace_info', params: [] },
            { pattern: /run command (.+)/i, tool: 'run_terminal_command', param: 'command' }
        ];

        for (const { pattern, tool, param, params } of toolPatterns) {
            const match = message.match(pattern);
            if (match) {
                if (params && Array.isArray(params) && params.length === 0) {
                    return { toolName: tool, parameters: {} };
                } else if (param) {
                    return { toolName: tool, parameters: { [param]: match[1] } };
                }
            }
        }

        return null;
    }

    private formatToolResult(result: any): string {
        if (typeof result === 'string') {
            return result;
        }
        return '```json\n' + JSON.stringify(result, null, 2) + '\n```';
    }

    private createContextPrompt(message: string, context?: any): string {
        let contextInfo = '';

        // Add file context if available
        const editor = vscode.window.activeTextEditor;
        if (editor) {
            const language = editor.document.languageId;
            const fileName = editor.document.fileName.split('/').pop();
            const selection = editor.selection;

            if (!selection.isEmpty) {
                const selectedText = editor.document.getText(selection);
                contextInfo += `\n**Current File:** ${fileName} (${language})\n**Selected Code:**\n\`\`\`${language}\n${selectedText}\n\`\`\`\n\n`;
            } else {
                contextInfo += `\n**Current File:** ${fileName} (${language})\n\n`;
            }
        }

        // Add workspace context if provided
        if (context?.workspaceInfo) {
            contextInfo += `**Workspace:** ${context.workspaceInfo}\n\n`;
        }

        // Add recent conversation history (last 4 messages for context)
        const recentMessages = this.currentSession.messages.slice(-4);
        if (recentMessages.length > 0) {
            contextInfo += '**Recent Conversation:**\n';
            recentMessages.forEach(msg => {
                const role = msg.role === 'user' ? 'You' : 'Assistant';
                const content = msg.content.length > 200 ?
                    msg.content.substring(0, 200) + '...' :
                    msg.content;
                contextInfo += `${role}: ${content}\n`;
            });
            contextInfo += '\n';
        }

        return `${contextInfo}**Current Question:** ${message}\n\n**Instructions:** Please provide a helpful, accurate response. If you're generating code, include proper syntax highlighting and explanations.`;
    }

    // Session Management
    private createNewSession(name?: string): ChatSession {
        const session: ChatSession = {
            id: this.generateSessionId(),
            name: name || `Chat ${this.sessions.size + 1}`,
            messages: [],
            created: Date.now(),
            lastUpdated: Date.now()
        };

        this.sessions.set(session.id, session);
        return session;
    }

    private createAndSwitchToNewSession(): void {
        this.currentSession = this.createNewSession();
        this.sendSessionUpdate();
        this.saveSessions();
    }

    private switchToSession(sessionId: string): void {
        const session = this.sessions.get(sessionId);
        if (session) {
            this.currentSession = session;
            this.sendSessionUpdate();
        }
    }

    private deleteSession(sessionId: string): void {
        if (this.sessions.size <= 1) {
            vscode.window.showWarningMessage('Cannot delete the last session');
            return;
        }

        this.sessions.delete(sessionId);

        if (this.currentSession.id === sessionId) {
            // Switch to first available session
            const firstSession = this.sessions.values().next().value;
            this.currentSession = firstSession;
        }

        this.sendSessionUpdate();
        this.saveSessions();
    }

    private clearCurrentSession(): void {
        this.currentSession.messages = [];
        this.currentSession.lastUpdated = Date.now();
        this.sendSessionUpdate();
        this.saveSessions();
    }

    // Message Management
    private async regenerateMessage(messageId: string): Promise<void> {
        const messageIndex = this.currentSession.messages.findIndex(m => m.id === messageId);
        if (messageIndex === -1 || messageIndex === 0) return;

        const message = this.currentSession.messages[messageIndex];
        if (message.role !== 'assistant') return;

        // Find the user message that prompted this response
        const userMessage = this.currentSession.messages[messageIndex - 1];
        if (userMessage.role !== 'user') return;

        // Remove the assistant message and regenerate
        this.currentSession.messages.splice(messageIndex, 1);
        await this.handleChatMessage(userMessage.content);
    }

    private async copyMessageToClipboard(content: string): Promise<void> {
        await vscode.env.clipboard.writeText(content);
        vscode.window.showInformationMessage('Message copied to clipboard');
    }

    private async exportCurrentSession(): Promise<void> {
        const sessionData = {
            name: this.currentSession.name,
            created: new Date(this.currentSession.created).toISOString(),
            messages: this.currentSession.messages.map(m => ({
                role: m.role,
                content: m.content,
                timestamp: new Date(m.timestamp).toISOString()
            }))
        };

        const doc = await vscode.workspace.openTextDocument({
            content: JSON.stringify(sessionData, null, 2),
            language: 'json'
        });

        await vscode.window.showTextDocument(doc);
    }

    // Utility Methods
    private generateMessageId(): string {
        return `msg_${++this.messageIdCounter}_${Date.now()}`;
    }

    private generateSessionId(): string {
        return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    private sendMessageUpdate(message: ChatMessage): void {
        if (this.webviewView) {
            this.webviewView.webview.postMessage({
                type: 'messageUpdate',
                message: message
            });
        }
    }

    private sendSessionUpdate(): void {
        if (this.webviewView) {
            this.webviewView.webview.postMessage({
                type: 'sessionUpdate',
                currentSession: this.currentSession,
                allSessions: Array.from(this.sessions.values())
            });
        }
    }

    private sendErrorMessage(error: string): void {
        const errorMessage: ChatMessage = {
            id: this.generateMessageId(),
            role: 'assistant',
            content: `❌ ${error}`,
            timestamp: Date.now(),
            error: error
        };

        this.currentSession.messages.push(errorMessage);
        this.sendMessageUpdate(errorMessage);
    }

    private sendInitialData(): void {
        if (this.webviewView) {
            const config = vscode.workspace.getConfiguration('hrm');
            this.webviewView.webview.postMessage({
                type: 'initialize',
                currentSession: this.currentSession,
                allSessions: Array.from(this.sessions.values()),
                config: {
                    theme: config.get<string>('chatTheme', 'auto'),
                    modelMode: config.get<string>('modelMode', 'hrm'),
                    enableStreaming: config.get<boolean>('enableStreaming', true)
                },
                modelStatus: {
                    loaded: this.modelLoader.isModelLoaded(),
                    stats: this.modelLoader.getModelStats()
                }
            });
        }
    }

    // Session Persistence
    private loadSessions(): void {
        try {
            const savedSessions = this.context.globalState.get<ChatSession[]>('hrm.chatSessions', []);
            savedSessions.forEach(session => {
                this.sessions.set(session.id, session);
            });

            if (savedSessions.length === 0) {
                // Create default session if none exist
                this.sessions.set(this.currentSession.id, this.currentSession);
            } else {
                // Load the most recent session
                const mostRecent = savedSessions.reduce((latest, session) =>
                    session.lastUpdated > latest.lastUpdated ? session : latest
                );
                this.currentSession = mostRecent;
            }
        } catch (error) {
            Logger.error('Failed to load chat sessions: ' + error);
        }
    }

    private saveSessions(): void {
        try {
            const sessionsArray = Array.from(this.sessions.values());
            this.context.globalState.update('hrm.chatSessions', sessionsArray);
        } catch (error) {
            Logger.error('Failed to save chat sessions: ' + error);
        }
    }

    private getHtmlForWebview(): string {
        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HRM Chat</title>
    <style>
        :root {
            --primary-color: var(--vscode-button-background);
            --primary-hover: var(--vscode-button-hoverBackground);
            --text-color: var(--vscode-foreground);
            --bg-color: var(--vscode-editor-background);
            --border-color: var(--vscode-panel-border);
            --user-bg: var(--vscode-inputValidation-infoBorder);
            --assistant-bg: var(--vscode-editor-background);
            --error-color: var(--vscode-errorForeground);
            --success-color: var(--vscode-terminal-ansiGreen);
            --warning-color: var(--vscode-terminal-ansiYellow);
            --code-bg: var(--vscode-textCodeBlock-background);
            --hover-bg: var(--vscode-list-hoverBackground);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--vscode-font-family);
            font-size: var(--vscode-font-size);
            color: var(--text-color);
            background: var(--bg-color);
            height: 100vh;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        /* Header */
        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 12px;
            border-bottom: 1px solid var(--border-color);
            background: var(--vscode-titleBar-activeBackground);
            flex-shrink: 0;
        }

        .model-indicator {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
            opacity: 0.8;
        }

        .model-status {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--success-color);
        }

        .model-status.disconnected {
            background: var(--error-color);
        }

        .header-actions {
            display: flex;
            gap: 4px;
        }

        .icon-button {
            background: none;
            border: none;
            color: var(--text-color);
            cursor: pointer;
            padding: 4px;
            border-radius: 3px;
            font-size: 14px;
            opacity: 0.7;
            transition: all 0.2s;
        }

        .icon-button:hover {
            opacity: 1;
            background: var(--hover-bg);
        }

        /* Chat Container */
        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .messages {
            flex: 1;
            overflow-y: auto;
            padding: 12px;
            scroll-behavior: smooth;
        }

        .message {
            margin-bottom: 16px;
            animation: fadeIn 0.3s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .message-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 6px;
            font-size: 12px;
            opacity: 0.8;
        }

        .message-avatar {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: bold;
        }

        .user-avatar {
            background: var(--user-bg);
            color: white;
        }

        .assistant-avatar {
            background: var(--primary-color);
            color: white;
        }

        .message-content {
            padding: 12px;
            border-radius: 8px;
            position: relative;
            line-height: 1.5;
        }

        .user-message .message-content {
            background: var(--user-bg);
            margin-left: 28px;
            border-bottom-left-radius: 4px;
        }

        .assistant-message .message-content {
            background: var(--assistant-bg);
            border: 1px solid var(--border-color);
            margin-right: 28px;
            border-bottom-right-radius: 4px;
        }

        .message-actions {
            position: absolute;
            top: 8px;
            right: 8px;
            display: none;
            gap: 4px;
        }

        .message:hover .message-actions {
            display: flex;
        }

        .message-time {
            font-size: 10px;
            opacity: 0.6;
        }

        .streaming-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--primary-color);
            animation: pulse 1.5s infinite;
            margin-left: 8px;
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 1; }
        }

        .error-message {
            border-left: 3px solid var(--error-color);
            background: var(--vscode-inputValidation-errorBackground);
        }

        /* Code Blocks */
        pre {
            background: var(--code-bg);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            padding: 12px;
            margin: 8px 0;
            overflow-x: auto;
            position: relative;
        }

        code {
            font-family: var(--vscode-editor-font-family);
            font-size: 13px;
        }

        .code-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 6px 12px;
            background: var(--vscode-titleBar-activeBackground);
            border-bottom: 1px solid var(--border-color);
            font-size: 12px;
            border-radius: 6px 6px 0 0;
            margin: -12px -12px 8px -12px;
        }

        .copy-button {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 11px;
            transition: background 0.2s;
        }

        .copy-button:hover {
            background: var(--primary-hover);
        }

        /* Input Area */
        .input-area {
            border-top: 1px solid var(--border-color);
            padding: 12px;
            background: var(--vscode-input-background);
            flex-shrink: 0;
        }

        .input-container {
            display: flex;
            gap: 8px;
            align-items: flex-end;
        }

        .input-wrapper {
            flex: 1;
            position: relative;
        }

        #messageInput {
            width: 100%;
            min-height: 36px;
            max-height: 120px;
            padding: 8px 40px 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 18px;
            background: var(--vscode-input-background);
            color: var(--text-color);
            font-family: inherit;
            font-size: inherit;
            resize: none;
            outline: none;
            transition: border-color 0.2s;
        }

        #messageInput:focus {
            border-color: var(--primary-color);
        }

        #messageInput::placeholder {
            color: var(--vscode-input-placeholderForeground);
        }

        .send-button {
            position: absolute;
            right: 6px;
            bottom: 6px;
            width: 28px;
            height: 28px;
            border: none;
            border-radius: 50%;
            background: var(--primary-color);
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
            font-size: 14px;
        }

        .send-button:hover:not(:disabled) {
            background: var(--primary-hover);
            transform: scale(1.05);
        }

        .send-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .input-actions {
            display: flex;
            gap: 4px;
        }

        /* Scrollbar */
        .messages::-webkit-scrollbar {
            width: 6px;
        }

        .messages::-webkit-scrollbar-track {
            background: transparent;
        }

        .messages::-webkit-scrollbar-thumb {
            background: var(--vscode-scrollbarSlider-background);
            border-radius: 3px;
        }

        .messages::-webkit-scrollbar-thumb:hover {
            background: var(--vscode-scrollbarSlider-hoverBackground);
        }

        /* Empty State */
        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            text-align: center;
            opacity: 0.6;
        }

        .empty-state-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }

        .empty-state-title {
            font-size: 18px;
            margin-bottom: 8px;
        }

        .empty-state-subtitle {
            font-size: 14px;
            opacity: 0.8;
        }

        /* Responsive */
        @media (max-width: 400px) {
            .header {
                padding: 6px 8px;
            }

            .messages {
                padding: 8px;
            }

            .input-area {
                padding: 8px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="model-indicator">
            <div class="model-status" id="modelStatus"></div>
            <span id="modelName">Loading...</span>
        </div>
        <div class="header-actions">
            <button class="icon-button" onclick="newSession()" title="New Chat">➕</button>
            <button class="icon-button" onclick="exportChat()" title="Export Chat">📤</button>
            <button class="icon-button" onclick="clearChat()" title="Clear Chat">🗑️</button>
        </div>
    </div>

    <div class="chat-container">
        <div class="messages" id="messages">
            <div class="empty-state" id="emptyState">
                <div class="empty-state-icon">🤖</div>
                <div class="empty-state-title">Welcome to HRM Chat</div>
                <div class="empty-state-subtitle">Start a conversation to get coding assistance</div>
            </div>
        </div>
    </div>

    <div class="input-area">
        <div class="input-container">
            <div class="input-wrapper">
                <textarea
                    id="messageInput"
                    placeholder="Ask me anything about coding..."
                    rows="1"
                    maxlength="4000"
                ></textarea>
                <button class="send-button" id="sendButton" onclick="sendMessage()" title="Send message">
                    ➤
                </button>
            </div>
        </div>
    </div>

    <script>
        const vscode = acquireVsCodeApi();
        let currentSession = null;
        let allSessions = [];
        let config = {};
        let modelStatus = {};

        // Initialize
        window.addEventListener('load', () => {
            vscode.postMessage({ type: 'ready' });
            setupEventListeners();
        });

        function setupEventListeners() {
            const input = document.getElementById('messageInput');
            const sendButton = document.getElementById('sendButton');

            // Auto-resize textarea
            input.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 120) + 'px';

                // Enable/disable send button
                sendButton.disabled = !this.value.trim();
            });

            // Send on Enter (but not Shift+Enter)
            input.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });

            // Handle paste events for file content
            input.addEventListener('paste', function(e) {
                // Future: Handle file paste
            });
        }

        // Message handling
        window.addEventListener('message', event => {
            const message = event.data;

            switch (message.type) {
                case 'initialize':
                    handleInitialize(message);
                    break;
                case 'messageUpdate':
                    handleMessageUpdate(message.message);
                    break;
                case 'sessionUpdate':
                    handleSessionUpdate(message);
                    break;
            }
        });

        function handleInitialize(data) {
            currentSession = data.currentSession;
            allSessions = data.allSessions;
            config = data.config;
            modelStatus = data.modelStatus;

            updateModelIndicator();
            renderMessages();
        }

        function handleMessageUpdate(message) {
            renderMessage(message);
            scrollToBottom();
        }

        function handleSessionUpdate(data) {
            currentSession = data.currentSession;
            allSessions = data.allSessions;
            renderMessages();
        }

        function updateModelIndicator() {
            const statusEl = document.getElementById('modelStatus');
            const nameEl = document.getElementById('modelName');

            if (modelStatus.loaded) {
                statusEl.className = 'model-status';
                nameEl.textContent = config.modelMode.toUpperCase();
            } else {
                statusEl.className = 'model-status disconnected';
                nameEl.textContent = 'Disconnected';
            }
        }

        function renderMessages() {
            const messagesEl = document.getElementById('messages');
            const emptyState = document.getElementById('emptyState');

            if (!currentSession || currentSession.messages.length === 0) {
                emptyState.style.display = 'flex';
                messagesEl.innerHTML = '';
                messagesEl.appendChild(emptyState);
                return;
            }

            emptyState.style.display = 'none';
            messagesEl.innerHTML = '';

            currentSession.messages.forEach(message => {
                renderMessage(message);
            });

            scrollToBottom();
        }

        function renderMessage(message) {
            const messagesEl = document.getElementById('messages');
            const emptyState = document.getElementById('emptyState');

            // Hide empty state
            if (emptyState) {
                emptyState.style.display = 'none';
            }

            // Find existing message or create new one
            let messageEl = document.getElementById('message-' + message.id);
            if (!messageEl) {
                messageEl = document.createElement('div');
                messageEl.id = 'message-' + message.id;
                messageEl.className = 'message ' + message.role + '-message';
                messagesEl.appendChild(messageEl);
            }

            const isUser = message.role === 'user';
            const avatar = isUser ? '👤' : '🤖';
            const name = isUser ? 'You' : 'Assistant';
            const time = new Date(message.timestamp).toLocaleTimeString();

            let content = formatMessageContent(message.content);
            let streamingIndicator = message.isStreaming ? '<span class="streaming-indicator"></span>' : '';
            let errorClass = message.error ? ' error-message' : '';

            messageEl.innerHTML = \`
                <div class="message-header">
                    <div class="message-avatar \${isUser ? 'user-avatar' : 'assistant-avatar'}">\${avatar}</div>
                    <span>\${name}</span>
                    <span class="message-time">\${time}</span>
                    \${streamingIndicator}
                </div>
                <div class="message-content\${errorClass}">
                    \${content}
                    <div class="message-actions">
                        <button class="icon-button" onclick="copyMessage('\${message.id}')" title="Copy">📋</button>
                        \${!isUser ? \`<button class="icon-button" onclick="regenerateMessage('\${message.id}')" title="Regenerate">🔄</button>\` : ''}
                    </div>
                </div>
            \`;
        }

        function formatMessageContent(content) {
            if (!content) return '';

            // Handle code blocks
            content = content.replace(/\`\`\`(\w+)?\n([\s\S]*?)\`\`\`/g, (match, lang, code) => {
                const language = lang || 'text';
                const copyId = 'copy-' + Math.random().toString(36).substr(2, 9);
                return \`
                    <pre>
                        <div class="code-header">
                            <span>\${language}</span>
                            <button class="copy-button" onclick="copyCode('\${copyId}')" title="Copy code">Copy</button>
                        </div>
                        <code id="\${copyId}">\${escapeHtml(code.trim())}</code>
                    </pre>
                \`;
            });

            // Handle inline code
            content = content.replace(/\`([^\`]+)\`/g, '<code>$1</code>');

            // Handle line breaks
            content = content.replace(/\n/g, '<br>');

            return content;
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function scrollToBottom() {
            const messagesEl = document.getElementById('messages');
            messagesEl.scrollTop = messagesEl.scrollHeight;
        }

        // User actions
        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();

            if (!message) return;

            // Get current editor context
            const context = {
                timestamp: Date.now()
            };

            vscode.postMessage({
                type: 'sendMessage',
                message: message,
                context: context
            });

            input.value = '';
            input.style.height = 'auto';
            document.getElementById('sendButton').disabled = true;
        }

        function clearChat() {
            if (confirm('Clear current chat? This cannot be undone.')) {
                vscode.postMessage({ type: 'clearChat' });
            }
        }

        function newSession() {
            vscode.postMessage({ type: 'newSession' });
        }

        function exportChat() {
            vscode.postMessage({ type: 'exportChat' });
        }

        function copyMessage(messageId) {
            const message = currentSession.messages.find(m => m.id === messageId);
            if (message) {
                vscode.postMessage({ type: 'copyMessage', content: message.content });
            }
        }

        function regenerateMessage(messageId) {
            vscode.postMessage({ type: 'regenerateMessage', messageId: messageId });
        }

        function copyCode(elementId) {
            const codeEl = document.getElementById(elementId);
            if (codeEl) {
                const text = codeEl.textContent;
                vscode.postMessage({ type: 'copyMessage', content: text });

                // Visual feedback
                const button = codeEl.parentElement.querySelector('.copy-button');
                const originalText = button.textContent;
                button.textContent = 'Copied!';
                setTimeout(() => {
                    button.textContent = originalText;
                }, 1000);
            }
        }
    </script>
</body>
</html>`;
    }
}
