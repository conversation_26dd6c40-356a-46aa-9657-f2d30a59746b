/**
 * HRM Model Loader for VSCode Extension
 * Loads and manages the safetensors HRM model locally
 */

import * as vscode from 'vscode';
import { Logger } from '../utils/logger';

export interface HRMModelConfig {
    model_name: string;
    version: string;
    architecture: string;
    config: {
        vocab_size: number;
        hidden_size: number;
        num_layers: number;
        num_attention_heads: number;
        max_sequence_length: number;
        multimodal_enabled: boolean;
        streaming_enabled: boolean;
    };
}

export interface ModelLoadResult {
    success: boolean;
    model?: HRMModel;
    error?: string;
}

export class HRMModel {
    private config: HRMModelConfig;
    private modelPath: string;
    private loaded: boolean = false;

    constructor(config: HRMModelConfig, modelPath: string) {
        this.config = config;
        this.modelPath = modelPath;
    }

    public getConfig(): HRMModelConfig {
        return this.config;
    }

    public isLoaded(): boolean {
        return this.loaded;
    }

    public getModelPath(): string {
        return this.modelPath;
    }

    public async generateText(prompt: string, options?: {
        maxTokens?: number;
        temperature?: number;
        stream?: boolean;
    }): Promise<string> {
        if (!this.loaded) {
            throw new Error('Model not loaded');
        }

        // For now, we'll use the Python model server
        // In the future, this could be replaced with direct model inference
        const response = await this.callPythonServer(prompt, options);
        return response;
    }

    private async callPythonServer(prompt: string, options?: any): Promise<string> {
        try {
            // Use the configured server URL
            const config = vscode.workspace.getConfiguration('hrm');
            const serverUrl = config.get('serverUrl', 'http://localhost:5001');
            
            const fetch = (await import('node-fetch')).default;
            const response = await fetch(`${serverUrl}/chat`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    messages: [{ role: 'user', content: prompt }],
                    max_tokens: options?.maxTokens || 1000,
                    temperature: options?.temperature || 0.7,
                    stream: options?.stream || false,
                    model_mode: this.getModelMode(),
                    model_path: this.getActiveModelPath()
                })
            });

            const data = await response.json() as any;
            return data.message?.content || 'No response generated';
        } catch (error) {
            Logger.error(`Model server call failed: ${error}`);
            return `Error: ${error}`;
        }
    }





    public setLoaded(loaded: boolean) {
        this.loaded = loaded;
    }

    private getModelMode(): string {
        const config = vscode.workspace.getConfiguration('hrm');
        return config.get<string>('modelMode', 'hrm');
    }

    private getActiveModelPath(): string | undefined {
        const config = vscode.workspace.getConfiguration('hrm');
        const mode = this.getModelMode();

        switch (mode) {
            case 'deepseek':
                return config.get<string>('deepseekModelPath', 'models/DeepSeek-Coder-V2-Lite-Base');
            case 'hf-local':
                return config.get<string>('hfLocalModelPath', '');
            default:
                return undefined; // HRM model uses default server path
        }
    }
}

export class ModelLoader {
    private static instance: ModelLoader;
    private currentModel: HRMModel | null = null;

    private constructor() {
        // Constructor now only initializes the instance
    }

    private getServerUrl(): string {
        const config = vscode.workspace.getConfiguration('hrm');
        return config.get('serverUrl', 'http://localhost:5002');
    }

    public static getInstance(): ModelLoader {
        if (!ModelLoader.instance) {
            ModelLoader.instance = new ModelLoader();
        }
        return ModelLoader.instance;
    }

    public async loadModel(): Promise<ModelLoadResult> {
        try {
            Logger.info('🔄 Connecting to HRM server...');

            // First, check if server is running
            const serverUrl = this.getServerUrl();
            try {
                const fetch = (await import('node-fetch')).default;
                const response = await fetch(`${serverUrl}/health`);
                if (!response.ok) {
                    throw new Error('Server not responding');
                }
                Logger.info('✅ HRM server is running');
            } catch (error) {
                Logger.info('❌ HRM server not running, please start it manually');
                Logger.info('💡 Run: python3 start_hrm_server.py');
                return { success: false, error: 'HRM server not running. Please start it with: python3 start_hrm_server.py' };
            }

            // Try to get model status from server
            try {
                const fetch = (await import('node-fetch')).default;
                const response = await fetch(`${serverUrl}/status`);
                const status = await response.json() as any;

                if (!status.model_loaded) {
                    const error = 'No model loaded on server';
                    Logger.error(error);
                    return { success: false, error };
                }

                // Create model config based on server status
                const modelConfig: HRMModelConfig = {
                    model_name: 'hrm_model',
                    version: 'v1.0.0',
                    architecture: 'HRM',
                    config: {
                        vocab_size: 32000,
                        hidden_size: 2048,
                        num_layers: 24,
                        num_attention_heads: 32,
                        max_sequence_length: 4096,
                        multimodal_enabled: true,
                        streaming_enabled: true
                    }
                };

                // Create model instance
                this.currentModel = new HRMModel(modelConfig, 'server-based');
                this.currentModel.setLoaded(true);

                Logger.info(`✅ Connected to HRM server successfully!`);
                Logger.info(`   • Model: ${modelConfig.model_name} ${modelConfig.version}`);
                Logger.info(`   • Server: ${serverUrl}`);
                Logger.info(`   • Status: Ready for inference`);

                // Set context for when clauses
                vscode.commands.executeCommand('setContext', 'hrm.modelLoaded', true);

                return { success: true, model: this.currentModel };

            } catch (error) {
                const errorMsg = `Failed to connect to server: ${error}`;
                Logger.error(errorMsg);
                return { success: false, error: errorMsg };
            }

        } catch (error) {
            const errorMsg = `Failed to load model: ${error}`;
            Logger.error(errorMsg);
            return { success: false, error: errorMsg };
        }
    }

    public getCurrentModel(): HRMModel | null {
        return this.currentModel;
    }

    public isModelLoaded(): boolean {
        return this.currentModel !== null && this.currentModel.isLoaded();
    }

    public async reloadModel(): Promise<ModelLoadResult> {
        this.currentModel = null;
        vscode.commands.executeCommand('setContext', 'hrm.modelLoaded', false);
        return await this.loadModel();
    }

    public updateModelPath(newPath: string) {
        // Update configuration only (no longer storing locally)
        const config = vscode.workspace.getConfiguration('hrm');
        config.update('modelPath', newPath, vscode.ConfigurationTarget.Global);
    }

    private getParameterCount(config: HRMModelConfig): number {
        // Rough estimation based on architecture
        const hiddenSize = config.config.hidden_size;
        const numLayers = config.config.num_layers;
        const vocabSize = config.config.vocab_size;
        
        // Simplified parameter count estimation
        const embeddingParams = vocabSize * hiddenSize;
        const layerParams = numLayers * (hiddenSize * hiddenSize * 4); // Rough estimate
        const totalParams = embeddingParams + layerParams;
        
        return Math.round(totalParams / 1000000); // Convert to millions
    }

    private getFeatureList(config: HRMModelConfig): string {
        const features = [];
        if (config.config.multimodal_enabled) features.push('Multi-modal');
        if (config.config.streaming_enabled) features.push('Streaming');
        features.push(`${config.config.num_layers} layers`);
        features.push(`${config.config.max_sequence_length} context`);
        return features.join(', ');
    }

    public getModelStats(): any {
        if (!this.currentModel) {
            return null;
        }

        const config = this.currentModel.getConfig();
        return {
            name: config.model_name,
            version: config.version,
            architecture: config.architecture,
            parameters: this.getParameterCount(config),
            features: this.getFeatureList(config),
            path: this.currentModel.getModelPath(),
            loaded: this.currentModel.isLoaded()
        };
    }
}
