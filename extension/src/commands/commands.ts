import * as vscode from 'vscode';
import { Model<PERSON>oader } from '../core/modelLoader';
import { HRMChatProvider } from '../ui/chatProvider';
import { HRMPlanningProvider } from '../providers/planningProvider';
import { MCPToolsManager } from '../tools/mcpTools';
import { Logger } from '../utils/logger';

export function registerCommands(
    context: vscode.ExtensionContext,
    modelLoader: ModelLoader,
    chatProvider: HRMChatProvider,
    planningProvider: HRMPlanningProvider,
    mcpToolsManager: MCPToolsManager
) {
    // Connect to server command
    const loadModelCommand = vscode.commands.registerCommand('hrm.loadModel', async () => {
        try {
            vscode.window.showInformationMessage('🔄 Connecting to HRM server...');
            const result = await modelLoader.loadModel();

            if (result.success) {
                vscode.window.showInformationMessage('✅ Connected to HRM server successfully!');
                // Update status bar
                vscode.commands.executeCommand('setContext', 'hrm.connected', true);
            } else {
                vscode.window.showWarningMessage(`❌ Cannot connect to HRM server. Please start it first:\n\npython3 start_hrm_server.py`);
            }
        } catch (error) {
            vscode.window.showWarningMessage(`❌ Connection failed. Please start the HRM server:\n\npython3 start_hrm_server.py`);
        }
    });

    // Generate code command
    const generateCodeCommand = vscode.commands.registerCommand('hrm.generateCode', async () => {
        if (!modelLoader.isModelLoaded()) {
            vscode.window.showWarningMessage('Please load HRM model first');
            return;
        }

        const prompt = await vscode.window.showInputBox({
            prompt: 'Describe the code you want to generate',
            placeHolder: 'e.g., write a function to calculate fibonacci numbers'
        });

        if (prompt) {
            try {
                const model = modelLoader.getCurrentModel();
                if (!model) return;

                const response = await model.generateText(prompt, {
                    maxTokens: 500,
                    temperature: 0.7
                });

                const editor = vscode.window.activeTextEditor;
                if (editor) {
                    await editor.edit(editBuilder => {
                        editBuilder.insert(editor.selection.active, response);
                    });
                    vscode.window.showInformationMessage('Code generated successfully!');
                } else {
                    vscode.window.showInformationMessage('Generated code:\n' + response);
                }
            } catch (error) {
                vscode.window.showErrorMessage(`Code generation failed: ${error}`);
            }
        }
    });

    // Plan task command
    const planTaskCommand = vscode.commands.registerCommand('hrm.planTask', async () => {
        if (!modelLoader.isModelLoaded()) {
            vscode.window.showWarningMessage('Please load HRM model first');
            return;
        }

        const taskDescription = await vscode.window.showInputBox({
            prompt: 'Describe the task you want to plan',
            placeHolder: 'e.g., create a REST API for user management'
        });

        if (taskDescription) {
            vscode.commands.executeCommand('hrmPlanning.focus');
            // The planning provider will handle the actual planning
        }
    });

    // Execute task command
    const executeTaskCommand = vscode.commands.registerCommand('hrm.executeTask', async () => {
        vscode.commands.executeCommand('hrmPlanning.focus');
        vscode.window.showInformationMessage('Use the Planning panel to execute tasks');
    });

    // Explain code command
    const explainCodeCommand = vscode.commands.registerCommand('hrm.explainCode', async () => {
        if (!modelLoader.isModelLoaded()) {
            vscode.window.showWarningMessage('Please load HRM model first');
            return;
        }

        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showWarningMessage('No active editor');
            return;
        }

        const selection = editor.selection;
        const selectedText = editor.document.getText(selection);

        if (!selectedText.trim()) {
            vscode.window.showWarningMessage('Please select some code to explain');
            return;
        }

        try {
            const model = modelLoader.getCurrentModel();
            if (!model) return;

            const prompt = `Explain the following code in detail:

\`\`\`${editor.document.languageId}
${selectedText}
\`\`\`

Provide a clear explanation of what this code does, how it works, and any important details:`;

            const explanation = await model.generateText(prompt, {
                maxTokens: 1000,
                temperature: 0.5
            });

            // Show explanation in a new document
            const doc = await vscode.workspace.openTextDocument({
                content: `Code Explanation\n================\n\nOriginal Code:\n\`\`\`${editor.document.languageId}\n${selectedText}\n\`\`\`\n\nExplanation:\n${explanation}`,
                language: 'markdown'
            });

            await vscode.window.showTextDocument(doc);
        } catch (error) {
            vscode.window.showErrorMessage(`Code explanation failed: ${error}`);
        }
    });

    // Refactor code command
    const refactorCodeCommand = vscode.commands.registerCommand('hrm.refactorCode', async () => {
        if (!modelLoader.isModelLoaded()) {
            vscode.window.showWarningMessage('Please load HRM model first');
            return;
        }

        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showWarningMessage('No active editor');
            return;
        }

        const selection = editor.selection;
        const selectedText = editor.document.getText(selection);

        if (!selectedText.trim()) {
            vscode.window.showWarningMessage('Please select some code to refactor');
            return;
        }

        const refactorType = await vscode.window.showQuickPick([
            'Improve readability',
            'Optimize performance',
            'Add error handling',
            'Extract functions',
            'Add comments',
            'Custom refactoring'
        ], {
            placeHolder: 'Select refactoring type'
        });

        if (!refactorType) return;

        let customInstruction = '';
        if (refactorType === 'Custom refactoring') {
            const input = await vscode.window.showInputBox({
                prompt: 'Describe how you want to refactor the code',
                placeHolder: 'e.g., make it more functional, add type hints'
            });
            if (!input) return;
            customInstruction = input;
        }

        try {
            const model = modelLoader.getCurrentModel();
            if (!model) return;

            const instruction = refactorType === 'Custom refactoring' ? customInstruction : refactorType;
            const prompt = `Refactor the following ${editor.document.languageId} code to ${instruction}:

\`\`\`${editor.document.languageId}
${selectedText}
\`\`\`

Provide only the refactored code, no explanations:`;

            const refactoredCode = await model.generateText(prompt, {
                maxTokens: 1000,
                temperature: 0.3
            });

            // Clean the response
            const cleanedCode = refactoredCode
                .replace(/```[\w]*\n?/g, '')
                .replace(/```/g, '')
                .trim();

            // Replace the selected code
            await editor.edit(editBuilder => {
                editBuilder.replace(selection, cleanedCode);
            });

            vscode.window.showInformationMessage('Code refactored successfully!');
        } catch (error) {
            vscode.window.showErrorMessage(`Code refactoring failed: ${error}`);
        }
    });

    // Generate tests command
    const generateTestsCommand = vscode.commands.registerCommand('hrm.generateTests', async () => {
        if (!modelLoader.isModelLoaded()) {
            vscode.window.showWarningMessage('Please load HRM model first');
            return;
        }

        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showWarningMessage('No active editor');
            return;
        }

        const selection = editor.selection;
        const selectedText = editor.document.getText(selection);

        if (!selectedText.trim()) {
            vscode.window.showWarningMessage('Please select some code to generate tests for');
            return;
        }

        try {
            const model = modelLoader.getCurrentModel();
            if (!model) return;

            const prompt = `Generate comprehensive unit tests for the following ${editor.document.languageId} code:

\`\`\`${editor.document.languageId}
${selectedText}
\`\`\`

Generate tests that cover:
- Normal cases
- Edge cases
- Error cases
- Different input types

Provide complete test code with appropriate test framework:`;

            const tests = await model.generateText(prompt, {
                maxTokens: 1500,
                temperature: 0.5
            });

            // Create a new test file
            const fileName = editor.document.fileName;
            const baseName = fileName.split('.').slice(0, -1).join('.');
            const extension = fileName.split('.').pop();

            let testFileName = `${baseName}.test.${extension}`;
            if (editor.document.languageId === 'python') {
                testFileName = `test_${baseName.split('/').pop()}.py`;
            }

            const doc = await vscode.workspace.openTextDocument({
                content: tests,
                language: editor.document.languageId
            });

            await vscode.window.showTextDocument(doc);
            vscode.window.showInformationMessage('Tests generated successfully!');
        } catch (error) {
            vscode.window.showErrorMessage(`Test generation failed: ${error}`);
        }
    });

    // Analyze workspace command
    const analyzeWorkspaceCommand = vscode.commands.registerCommand('hrm.analyzeWorkspace', async () => {
        if (!modelLoader.isModelLoaded()) {
            vscode.window.showWarningMessage('Please load HRM model first');
            return;
        }

        try {
            const workspaceInfo = await mcpToolsManager.executeTool('get_workspace_info', {});

            const model = modelLoader.getCurrentModel();
            if (!model) return;

            const prompt = `Analyze the following workspace and provide insights:

Workspace Information:
${JSON.stringify(workspaceInfo, null, 2)}

Provide analysis including:
- Project structure overview
- Technologies used
- Potential improvements
- Code organization suggestions
- Best practices recommendations

Analysis:`;

            const analysis = await model.generateText(prompt, {
                maxTokens: 1500,
                temperature: 0.7
            });

            // Show analysis in a new document
            const doc = await vscode.workspace.openTextDocument({
                content: `Workspace Analysis\n==================\n\n${analysis}`,
                language: 'markdown'
            });

            await vscode.window.showTextDocument(doc);
        } catch (error) {
            vscode.window.showErrorMessage(`Workspace analysis failed: ${error}`);
        }
    });

    // Open chat command
    const openChatCommand = vscode.commands.registerCommand('hrm.openChat', () => {
        vscode.commands.executeCommand('hrmChat.focus');
    });
    // Select model (HRM, DeepSeek, or Local HF)
    const selectModelCommand = vscode.commands.registerCommand('hrm.selectModel', async () => {
        const config = vscode.workspace.getConfiguration('hrm');
        const currentMode = config.get<string>('modelMode', 'hrm');

        const pick = await vscode.window.showQuickPick([
            {
                label: 'HRM Model',
                description: 'Custom trained HRM model with multimodal capabilities',
                detail: 'Best for: General coding, planning, and multimodal tasks',
                mode: 'hrm',
                icon: '$(robot)'
            },
            {
                label: 'DeepSeek Coder V2',
                description: 'Specialized code generation model with MoE architecture',
                detail: 'Best for: Code generation, debugging, and code understanding',
                mode: 'deepseek',
                icon: '$(code)'
            },
            {
                label: 'Custom HuggingFace Model',
                description: 'Use a custom local HuggingFace model',
                detail: 'Best for: Specialized use cases with custom models',
                mode: 'hf-local',
                icon: '$(settings-gear)'
            }
        ], {
            placeHolder: `Select active model (current: ${currentMode.toUpperCase()})`,
            matchOnDescription: true,
            matchOnDetail: true
        });

        if (!pick) return;

        await config.update('modelMode', pick.mode, vscode.ConfigurationTarget.Global);

        if (pick.mode === 'hf-local') {
            const path = await vscode.window.showInputBox({
                prompt: 'Enter path to local HF model folder',
                value: config.get<string>('hfLocalModelPath', ''),
                placeHolder: 'e.g., models/my-custom-model'
            });
            if (path) {
                await config.update('hfLocalModelPath', path, vscode.ConfigurationTarget.Global);
            }
        }

        // Update status bar and reload model
        vscode.window.showInformationMessage(`${pick.icon} Active model set to ${pick.label}`);

        // Trigger model reload
        if (modelLoader.isModelLoaded()) {
            vscode.window.showInformationMessage('Reloading model with new selection...');
            await modelLoader.reloadModel();
        }
    });

    // Quick toggle between HRM and DeepSeek
    const toggleModelCommand = vscode.commands.registerCommand('hrm.toggleModel', async () => {
        const config = vscode.workspace.getConfiguration('hrm');
        const currentMode = config.get<string>('modelMode', 'hrm');

        const newMode = currentMode === 'hrm' ? 'deepseek' : 'hrm';
        await config.update('modelMode', newMode, vscode.ConfigurationTarget.Global);

        const modelName = newMode === 'hrm' ? 'HRM Model' : 'DeepSeek Coder V2';
        const icon = newMode === 'hrm' ? '$(robot)' : '$(code)';

        vscode.window.showInformationMessage(`${icon} Switched to ${modelName}`);

        // Trigger model reload
        if (modelLoader.isModelLoaded()) {
            await modelLoader.reloadModel();
        }
    });

    // Show model status
    const modelStatusCommand = vscode.commands.registerCommand('hrm.modelStatus', async () => {
        const config = vscode.workspace.getConfiguration('hrm');
        const currentMode = config.get<string>('modelMode', 'hrm');
        const stats = modelLoader.getModelStats();

        let statusMessage = `**Current Model:** ${currentMode.toUpperCase()}\n`;

        if (stats) {
            statusMessage += `**Status:** Connected ✅\n`;
            statusMessage += `**Model:** ${stats.name} ${stats.version}\n`;
            statusMessage += `**Architecture:** ${stats.architecture}\n`;
            statusMessage += `**Parameters:** ~${stats.parameters}M\n`;
            statusMessage += `**Features:** ${stats.features}\n`;
        } else {
            statusMessage += `**Status:** Disconnected ❌\n`;
            statusMessage += `**Action:** Run "Load HRM Model" command to connect\n`;
        }

        const action = await vscode.window.showInformationMessage(
            statusMessage,
            { modal: true },
            'Switch Model',
            'Reload Model'
        );

        if (action === 'Switch Model') {
            vscode.commands.executeCommand('hrm.selectModel');
        } else if (action === 'Reload Model') {
            vscode.commands.executeCommand('hrm.loadModel');
        }
    });


    // Register all commands
    context.subscriptions.push(
        loadModelCommand,
        generateCodeCommand,
        planTaskCommand,
        executeTaskCommand,
        explainCodeCommand,
        refactorCodeCommand,
        generateTestsCommand,
        analyzeWorkspaceCommand,
        openChatCommand,
        selectModelCommand,
        toggleModelCommand,
        modelStatusCommand
    );

    Logger.info('✅ All HRM commands registered successfully');
}
