/**
 * HRM Coding Assistant VSCode Extension
 * Main entry point for the extension with agentic capabilities
 */

import * as vscode from 'vscode';
import { ModelLoader } from './core/modelLoader';
import { HRMCompletionManager } from './providers/completionProvider';
import { HRMPlanningProvider } from './providers/planningProvider';
import { MCPToolsManager } from './tools/mcpTools';
import { HRMChatProvider } from './ui/chatProvider';
import { HRMStatusProvider } from './ui/statusProvider';
import { registerCommands } from './commands/commands';
import { Logger } from './utils/logger';

let modelLoader: ModelLoader;
let completionManager: HRMCompletionManager;
let planningProvider: HRMPlanningProvider;
let mcpToolsManager: MCPToolsManager;
let chatProvider: HRMChatProvider;
let statusProvider: HRMStatusProvider;
let statusBarItem: vscode.StatusBarItem;

export function activate(context: vscode.ExtensionContext) {
    Logger.info('🚀 HRM Coding Assistant extension is activating...');

    // Initialize core components
    modelLoader = ModelLoader.getInstance();
    completionManager = new HRMCompletionManager();
    planningProvider = new HRMPlanningProvider(context);
    mcpToolsManager = new MCPToolsManager();
    chatProvider = new HRMChatProvider(context, modelLoader, mcpToolsManager);
    statusProvider = new HRMStatusProvider(modelLoader);

    // Create status bar item
    statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100);
    statusBarItem.command = 'hrm.loadModel';
    statusBarItem.text = '$(robot) HRM: Starting...';
    statusBarItem.tooltip = 'HRM Coding Assistant - Connecting to server...';
    statusBarItem.show();

    // Register webview providers
    context.subscriptions.push(
        vscode.window.registerWebviewViewProvider('hrmChat', chatProvider)
    );

    context.subscriptions.push(
        vscode.window.registerWebviewViewProvider('hrmPlanning', planningProvider)
    );

    context.subscriptions.push(
        vscode.window.registerTreeDataProvider('hrmStatus', statusProvider)
    );

    // Activate completion provider
    completionManager.activate(context);

    // Register commands
    registerCommands(context, modelLoader, chatProvider, planningProvider, mcpToolsManager);

    // Auto-connect to server on startup
    setTimeout(async () => {
        try {
            statusBarItem.text = '$(loading~spin) HRM: Connecting...';
            const result = await modelLoader.loadModel();

            if (result.success) {
                updateStatusBar(true);
                Logger.info('✅ HRM server connected successfully');
                vscode.window.showInformationMessage('🤖 HRM Coding Assistant connected! Server is ready.');
            } else {
                updateStatusBar(false);
                Logger.error(`❌ Failed to connect to HRM server: ${result.error}`);
                vscode.window.showWarningMessage(`HRM Server not running. Start it with: python3 start_hrm_server.py`);
            }
        } catch (error) {
            updateStatusBar(false);
            Logger.error(`❌ Connection error: ${error}`);
            vscode.window.showWarningMessage(`HRM Server not running. Start it with: python3 start_hrm_server.py`);
        }
    }, 2000);



    // Setup configuration listener
    setupConfigurationListener(context);

    // Register status bar item for disposal
    context.subscriptions.push(statusBarItem);

    Logger.info('✅ HRM Coding Assistant extension activated successfully');
}

export function deactivate() {
    Logger.info('🔄 HRM Coding Assistant extension is deactivating...');

    if (completionManager) {
        completionManager.deactivate();
    }

    if (statusBarItem) {
        statusBarItem.dispose();
    }

    Logger.info('✅ HRM Coding Assistant extension deactivated');
}

function updateStatusBar(connected: boolean) {
    const config = vscode.workspace.getConfiguration('hrm');
    const modelMode = config.get<string>('modelMode', 'hrm');
    const showModelInStatusBar = config.get<boolean>('showModelInStatusBar', true);

    let modelIcon = '$(robot)';
    let modelName = 'HRM';

    switch (modelMode) {
        case 'deepseek':
            modelIcon = '$(code)';
            modelName = 'DeepSeek';
            break;
        case 'hf-local':
            modelIcon = '$(settings-gear)';
            modelName = 'Custom';
            break;
        default:
            modelIcon = '$(robot)';
            modelName = 'HRM';
    }

    if (connected) {
        statusBarItem.text = showModelInStatusBar ?
            `${modelIcon} ${modelName}: Connected` :
            `${modelIcon} Connected`;
        statusBarItem.tooltip = `${modelName} model connected and ready\nClick to open chat\nRight-click to switch models`;
        statusBarItem.command = 'hrm.openChat';
        statusBarItem.backgroundColor = undefined;
    } else {
        statusBarItem.text = showModelInStatusBar ?
            `${modelIcon} ${modelName}: Disconnected` :
            `${modelIcon} Disconnected`;
        statusBarItem.tooltip = `${modelName} model server not running\nStart with: python3 start_hrm_server.py\nClick to retry connection`;
        statusBarItem.command = 'hrm.loadModel';
        statusBarItem.backgroundColor = new vscode.ThemeColor('statusBarItem.warningBackground');
    }
}

// Listen for configuration changes to update status bar
function setupConfigurationListener(context: vscode.ExtensionContext) {
    const configListener = vscode.workspace.onDidChangeConfiguration(event => {
        if (event.affectsConfiguration('hrm.modelMode') ||
            event.affectsConfiguration('hrm.showModelInStatusBar')) {
            updateStatusBar(modelLoader.isModelLoaded());
        }
    });

    context.subscriptions.push(configListener);
}
