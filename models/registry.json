{"models": {"hrm_model_v1.0.0": {"model_name": "hrm_model", "version": "v1.0.0", "path": "models/hrm_model_v1.0.0", "registered_at": "2025-09-03T22:12:46.548971", "metadata": {"model_type": "initial_hrm_model", "created_by": "create_initial_model.py", "purpose": "Production HRM model for VSCode extension and chat interface", "model_size": "medium", "total_parameters": 1755543558, "trainable_parameters": 1755543558, "estimated_size_gb": 6.5399093851447105, "features": {"multimodal": true, "streaming": true, "memory_enabled": true, "vector_db_integration": true, "mcp_tools_integration": true}, "architecture": {"hidden_size": 2048, "num_layers": 24, "num_attention_heads": 32, "intermediate_size": 8192, "vocab_size": 32000, "max_sequence_length": 4096}}, "config": {"vocab_size": 32000, "hidden_size": 2048, "num_layers": 24, "num_attention_heads": 32, "intermediate_size": 8192, "max_sequence_length": 4096, "memory_size": 512, "streaming_enabled": true, "memory_layers": [3, 7, 11, 15, 19, 23], "multimodal_enabled": true, "audio_embedding_dim": 768, "video_embedding_dim": 768, "num_reasoning_layers": 4, "reasoning_heads": 8, "dropout": 0.1, "layer_norm_eps": 1e-05, "initializer_range": 0.02, "use_cache": true, "vector_db_enabled": true, "mcp_tools_enabled": true}, "created_at": "2025-09-03T22:12:46.498721"}, "deepseek_coder_v2_lite": {"model_name": "deepseek_coder_v2_lite", "version": "v2.0.0", "path": "models/DeepSeek-Coder-V2-Lite-Base", "registered_at": "2025-09-14T00:00:00.000000", "metadata": {"model_type": "deepseek_v2_coder", "created_by": "DeepSeek-AI", "purpose": "Advanced code generation and understanding with MoE architecture", "model_size": "large", "total_parameters": 15700000000, "trainable_parameters": 15700000000, "estimated_size_gb": 8.9, "features": {"multimodal": false, "streaming": true, "memory_enabled": false, "vector_db_integration": true, "mcp_tools_integration": true, "code_specialized": true, "moe_architecture": true}, "architecture": {"hidden_size": 2048, "num_layers": 27, "num_attention_heads": 16, "intermediate_size": 10944, "vocab_size": 102400, "max_sequence_length": 163840, "n_routed_experts": 64, "n_shared_experts": 2, "num_experts_per_tok": 6}}, "config": {"vocab_size": 102400, "hidden_size": 2048, "num_layers": 27, "num_attention_heads": 16, "intermediate_size": 10944, "max_sequence_length": 163840, "streaming_enabled": true, "use_cache": true, "vector_db_enabled": true, "mcp_tools_enabled": true, "model_type": "deepseek_v2", "moe_intermediate_size": 1408, "n_routed_experts": 64, "n_shared_experts": 2, "num_experts_per_tok": 6, "rope_theta": 10000, "rms_norm_eps": 1e-06, "torch_dtype": "bfloat16"}, "created_at": "2025-09-14T00:00:00.000000"}}, "active_model": "hrm_model_v1.0.0", "created_at": "2025-09-03T22:11:45.746423", "last_updated": "2025-09-14T00:00:00.000000"}