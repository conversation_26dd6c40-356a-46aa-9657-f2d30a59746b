{"architectures": ["DeepseekV2ForCausalLM"], "attention_bias": false, "attention_dropout": 0.0, "auto_map": {"AutoConfig": "configuration_deepseek.DeepseekV2Config", "AutoModel": "modeling_deepseek.DeepseekV2Model", "AutoModelForCausalLM": "modeling_deepseek.DeepseekV2ForCausalLM"}, "aux_loss_alpha": 0.001, "bos_token_id": 100000, "eos_token_id": 100001, "first_k_dense_replace": 1, "hidden_act": "silu", "hidden_size": 2048, "initializer_range": 0.02, "intermediate_size": 10944, "kv_lora_rank": 512, "max_position_embeddings": 163840, "model_type": "deepseek_v2", "moe_intermediate_size": 1408, "moe_layer_freq": 1, "n_group": 1, "n_routed_experts": 64, "n_shared_experts": 2, "norm_topk_prob": false, "num_attention_heads": 16, "num_experts_per_tok": 6, "num_hidden_layers": 27, "num_key_value_heads": 16, "pretraining_tp": 1, "q_lora_rank": null, "qk_nope_head_dim": 128, "qk_rope_head_dim": 64, "rms_norm_eps": 1e-06, "rope_scaling": {"beta_fast": 32, "beta_slow": 1, "factor": 40, "mscale": 0.707, "mscale_all_dim": 0.707, "original_max_position_embeddings": 4096, "type": "yarn"}, "rope_theta": 10000, "routed_scaling_factor": 1.0, "scoring_func": "softmax", "seq_aux": true, "tie_word_embeddings": false, "topk_group": 1, "topk_method": "greedy", "torch_dtype": "bfloat16", "transformers_version": "4.39.3", "use_cache": true, "v_head_dim": 128, "vocab_size": 102400}