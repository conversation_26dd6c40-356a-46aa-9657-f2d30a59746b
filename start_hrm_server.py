#!/usr/bin/env python3
"""
Simple HRM Server Startup Script
Starts the server with proper configuration for VSCode extension
"""

import sys
import logging
from pathlib import Path
import uvicorn

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.server import RealHRMServer

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """Start the HRM server"""
    logger.info("🚀 Starting HRM Server for VSCode Extension...")
    
    try:
        # Create server instance
        server = RealHRMServer(
            model_path="models/hrm_model_v1.0.0",  # Default to HRM model
            port=5002  # Use port 5002 as configured in extension
        )
        
        logger.info("🌐 Server starting on http://localhost:5002")
        logger.info("📖 API docs: http://localhost:5002/docs")
        logger.info("🔍 Health check: http://localhost:5002/health")
        logger.info("💡 Use Ctrl+C to stop the server")
        
        # Start server
        uvicorn.run(
            server.app,
            host="0.0.0.0",
            port=5002,
            reload=False,
            workers=1,
            log_level="info"
        )
        
    except KeyboardInterrupt:
        logger.info("🛑 Server stopped by user")
    except Exception as e:
        logger.error(f"❌ Server startup failed: {e}")
        logger.error("💡 Make sure you have all dependencies installed:")
        logger.error("   pip install torch transformers fastapi uvicorn")
        sys.exit(1)

if __name__ == "__main__":
    main()
